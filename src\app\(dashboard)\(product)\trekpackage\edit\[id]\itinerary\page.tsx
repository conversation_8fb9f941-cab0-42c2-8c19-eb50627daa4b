"use client"

import { useCreatePackageItinerary } from "@/modules/package-itinerary/mutations/create-package-itinerary"
import { useDeletePackageItinerary } from "@/modules/package-itinerary/mutations/delete-package-itinerary"
import { useUpdatePackageItinerary } from "@/modules/package-itinerary/mutations/update-package-itinerary"
import { useGetPackageItineraryById } from "@/modules/package-itinerary/queries/get-package-itinerary-by-id"
import { AddItineraryForm } from "@/modules/product/component/add-itinerary-form"
import { EditTabs } from "@/modules/product/component/edit-tabs"
import { ItineraryList } from "@/modules/product/component/itinerary-list"
import { useParams, useRouter } from "next/navigation"
import { useEffect, useState } from "react"

export default function EditItineraryPage() {
  const { packageId } = useParams() as { packageId: string }
  const router = useRouter()

  const { data, isLoading, isError } = useGetPackageItineraryById(packageId)

  const createMutation = useCreatePackageItinerary()
  const updateMutation = useUpdatePackageItinerary(packageId)
  const deleteMutation = useDeletePackageItinerary(packageId)

  const [items, setItems] = useState(data?.data || [])
  const [editingItem, setEditingItem] = useState(null)

  useEffect(() => {
    if (data?.data) setItems(data.data)
  }, [data])

  const onAddItinerary = (newItem) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: (res) => setItems([...items, res.data])
      }
    )
  }

  const onUpdateItinerary = (updatedItem) => {
    updateMutation.mutate(
      { ...updatedItem, packageId },
      {
        onSuccess: (res) => {
          setItems(items.map(i => i.id === res.data.id ? res.data : i))
          setEditingItem(null)
        }
      }
    )
  }

  const onDeleteItinerary = (id) => {
    deleteMutation.mutate(id, {
      onSuccess: () => setItems(items.filter(i => i.id !== id))
    })
  }

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddItineraryForm
          editingItem={editingItem}
          onAddItinerary={onAddItinerary}
          onUpdateItinerary={onUpdateItinerary}
          onCancelEdit={() => setEditingItem(null)}
        />
        <ItineraryList
          items={items}
          onEdit={(id) => setEditingItem(items.find(i => i.id === id))}
          onDelete={onDeleteItinerary}
        />
      </div>
    </div>
  )
}
